---
layout: base.njk
title: Home
---

<!-- View Controls -->
<div class="view-controls">
    <div class="view-modes">
        {% for mode in site.viewModes %}
        <button 
            class="view-mode-btn{% if loop.first %} active{% endif %}" 
            data-view="{{ mode.id }}"
            title="{{ mode.name }}"
        >
            <i class="{{ mode.icon }}"></i>
        </button>
        {% endfor %}
    </div>
    
    <div class="results-info">
        <span id="resultsCount">{{ videos.length }} videos</span>
    </div>
</div>

<!-- Video Grid -->
<div class="video-grid" id="videoGrid" data-view="large-icons">
    {% if videos.length > 0 %}
        {% for video in videos %}
        <div class="video-card fade-in" data-video-id="{{ video.id }}" data-tags="{{ video.tags | join(',') | lower }}">
            <div class="video-thumbnail">
                <img 
                    src="https://img.youtube.com/vi/{{ video.url | youtubeId }}/maxresdefault.jpg" 
                    alt="{{ video.title }}"
                    loading="lazy"
                >
                <div class="play-overlay">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            
            <div class="video-info">
                <h3 class="video-title">{{ video.title }}</h3>
                
                {% if video.tags.length > 0 %}
                <div class="video-tags">
                    {% for tag in video.tags %}
                    <span class="tag">{{ tag | capitalize }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <p class="video-date">{{ video.dateAdded | dateFormat }}</p>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-state">
            <div class="empty-state-content">
                <i class="fas fa-video empty-state-icon"></i>
                <h2>No videos yet</h2>
                <p>Start building your collection by adding your first YouTube video!</p>
                <button class="btn add-first-video-btn">
                    <i class="fas fa-plus"></i>
                    Add Your First Video
                </button>
            </div>
        </div>
    {% endif %}
</div>

<!-- Loading State -->
<div class="loading-state" id="loadingState" style="display: none;">
    <div class="loading"></div>
    <p>Loading videos...</p>
</div>

<!-- No Results State -->
<div class="no-results-state" id="noResultsState" style="display: none;">
    <div class="empty-state-content">
        <i class="fas fa-search empty-state-icon"></i>
        <h2>No videos found</h2>
        <p>Try adjusting your search terms or browse all videos.</p>
        <button class="btn clear-search-btn" id="clearSearchBtn">
            <i class="fas fa-times"></i>
            Clear Search
        </button>
    </div>
</div>

<style>
/* Empty State Styles */
.empty-state {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    text-align: center;
}

.empty-state-content {
    max-width: 400px;
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.empty-state h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.loading-state,
.no-results-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    text-align: center;
    color: var(--text-secondary);
}

.loading-state p,
.no-results-state p {
    margin-top: 1rem;
}

/* View Mode Specific Styles */
.video-grid[data-view="small-icons"] {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.video-grid[data-view="small-icons"] .video-thumbnail {
    height: 120px;
}

.video-grid[data-view="small-icons"] .video-title {
    font-size: 0.9rem;
    -webkit-line-clamp: 1;
}

.video-grid[data-view="list"] {
    grid-template-columns: 1fr;
    gap: 1rem;
}

.video-grid[data-view="list"] .video-card {
    display: flex;
    height: 120px;
}

.video-grid[data-view="list"] .video-thumbnail {
    width: 200px;
    height: 120px;
    flex-shrink: 0;
}

.video-grid[data-view="list"] .video-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.video-grid[data-view="details"] {
    grid-template-columns: 1fr;
    gap: 1rem;
}

.video-grid[data-view="details"] .video-card {
    display: flex;
    padding: 1rem;
}

.video-grid[data-view="details"] .video-thumbnail {
    width: 160px;
    height: 90px;
    flex-shrink: 0;
}

.video-grid[data-view="details"] .video-info {
    flex: 1;
    margin-left: 1rem;
}

.video-grid[data-view="tiles"] {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.video-grid[data-view="tiles"] .video-card {
    display: flex;
    flex-direction: row;
    height: 100px;
}

.video-grid[data-view="tiles"] .video-thumbnail {
    width: 120px;
    height: 100px;
    flex-shrink: 0;
}

.video-grid[data-view="tiles"] .video-info {
    flex: 1;
    padding: 0.75rem;
}

.video-grid[data-view="tiles"] .video-title {
    font-size: 0.9rem;
    -webkit-line-clamp: 2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .video-grid[data-view="list"] .video-card,
    .video-grid[data-view="details"] .video-card,
    .video-grid[data-view="tiles"] .video-card {
        flex-direction: column;
        height: auto;
    }
    
    .video-grid[data-view="list"] .video-thumbnail,
    .video-grid[data-view="details"] .video-thumbnail,
    .video-grid[data-view="tiles"] .video-thumbnail {
        width: 100%;
        height: 200px;
    }
    
    .video-grid[data-view="details"] .video-info,
    .video-grid[data-view="tiles"] .video-info {
        margin-left: 0;
        padding: 1rem;
    }
}
</style>
