/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #ff0000;
  --primary-dark: #cc0000;
  --secondary-color: #282828;
  --background-color: #0f0f0f;
  --surface-color: #1f1f1f;
  --text-primary: #ffffff;
  --text-secondary: #aaaaaa;
  --border-color: #333333;
  --hover-color: #333333;
  --success-color: #00ff00;
  --border-radius: 12px;
  --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Header */
.header {
  background: var(--surface-color);
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-container {
  flex: 1;
  max-width: 500px;
  margin: 0 2rem;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--background-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.add-video-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-video-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

/* View Mode Controls */
.view-controls {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-modes {
  display: flex;
  gap: 0.5rem;
  background: var(--surface-color);
  padding: 0.5rem;
  border-radius: var(--border-radius);
}

.view-mode-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
}

.view-mode-btn.active,
.view-mode-btn:hover {
  background: var(--primary-color);
  color: white;
}

/* Video Grid */
.video-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Video Card */
.video-card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid var(--border-color);
  opacity: 1;
  transform: translateY(0);
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow);
  border-color: var(--primary-color);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  opacity: 0;
  transition: var(--transition);
}

.video-card:hover .play-overlay {
  opacity: 1;
}

.video-info {
  padding: 1rem;
}

.video-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.tag {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.video-date {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
}

.close-btn:hover {
  color: var(--text-primary);
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  background: var(--background-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Tag Suggestions */
.tag-suggestions {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  max-height: 200px;
  overflow-y: auto;
  margin-top: 0.5rem;
  display: none;
}

.tag-suggestions.active {
  display: block;
}

.tag-suggestion {
  padding: 0.75rem;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: 1px solid var(--border-color);
}

.tag-suggestion:last-child {
  border-bottom: none;
}

.tag-suggestion:hover,
.tag-suggestion.active {
  background: var(--hover-color);
}

/* Popular Tags */
.popular-tags {
  margin-top: 1rem;
}

.popular-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.popular-tag {
  background: var(--surface-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: var(--transition);
}

.popular-tag:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.popular-tag.added {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-container {
    margin: 0;
    max-width: none;
  }
  
  .view-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .video-grid {
    grid-template-columns: 1fr;
    padding: 0 1rem 2rem;
  }
  
  .modal-content {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Video Detail Modal Styles */
.video-detail-content {
  max-width: 800px;
  width: 95%;
}

.video-player-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.video-player-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-detail-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.video-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.video-actions .btn {
  flex: 1;
  min-width: 150px;
  justify-content: center;
}

/* Video Preview in Add Modal */
.video-preview-card {
  display: flex;
  background: var(--background-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.video-preview-card img {
  width: 120px;
  height: 90px;
  object-fit: cover;
  flex-shrink: 0;
}

.preview-info {
  padding: 1rem;
  flex: 1;
}

.preview-info h3 {
  font-size: 1rem;
  margin: 0;
  color: var(--text-primary);
}

/* Notification Styles */
.notification {
  font-weight: 500;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notification::before {
  content: '✓';
  font-weight: bold;
}

/* Enhanced Button Styles */
.btn-secondary {
  background: var(--surface-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--hover-color);
  border-color: var(--primary-color);
}

/* Enhanced Form Styles */
.form-input:invalid {
  border-color: #ff4444;
}

.form-input:valid {
  border-color: var(--success-color);
}

/* Enhanced Modal Animations */
.modal {
  animation: modalFadeIn 0.3s ease-out;
}

.modal.active .modal-content {
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Video Card Hover Effects */
.video-card {
  position: relative;
  overflow: hidden;
}

.video-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 0, 0, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 1;
}

.video-card:hover::before {
  transform: translateX(100%);
}

/* Search Highlighting */
mark {
  background: var(--primary-color);
  color: white;
  padding: 0.1rem 0.2rem;
  border-radius: 3px;
  font-weight: 600;
}

/* Search Input Enhancement */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-color);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
