<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home - YouTube Link Saver</title>
    <meta name="description" content="A clean, minimal way to save and organize your favorite YouTube videos">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/all.min.css">
    <link rel="stylesheet" href="/swiper-bundle.min.css">
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://img.youtube.com">
    <link rel="preconnect" href="https://i.ytimg.com">
</head>
<body data-barba="wrapper">
    <div data-barba="container" data-barba-namespace="home">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="/" class="logo">
                    <i class="fab fa-youtube"></i>
                    YouTube Link Saver
                </a>
                
                <div class="search-container">
                    <input 
                        type="text" 
                        class="search-input" 
                        placeholder="Search videos..." 
                        id="searchInput"
                        autocomplete="off"
                    >
                </div>
                
                <button class="add-video-btn" id="addVideoBtn">
                    <i class="fas fa-plus"></i>
                    Add Video
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            
<!-- View Controls -->
<div class="view-controls">
    <div class="view-modes">
        
        <button 
            class="view-mode-btn active" 
            data-view="large-icons"
            title="Large Icons"
        >
            <i class="fas fa-th-large"></i>
        </button>
        
        <button 
            class="view-mode-btn" 
            data-view="small-icons"
            title="Small Icons"
        >
            <i class="fas fa-th"></i>
        </button>
        
        <button 
            class="view-mode-btn" 
            data-view="list"
            title="List View"
        >
            <i class="fas fa-list"></i>
        </button>
        
        <button 
            class="view-mode-btn" 
            data-view="details"
            title="Details View"
        >
            <i class="fas fa-list-ul"></i>
        </button>
        
        <button 
            class="view-mode-btn" 
            data-view="tiles"
            title="Tiles View"
        >
            <i class="fas fa-grip-horizontal"></i>
        </button>
        
    </div>
    
    <div class="results-info">
        <span id="resultsCount">3 videos</span>
    </div>
</div>

<!-- Video Grid -->
<div class="video-grid" id="videoGrid" data-view="large-icons">
    
        
        <div class="video-card fade-in" data-video-id="1" data-tags="music,classic,80s,pop">
            <div class="video-thumbnail">
                <img 
                    src="https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg" 
                    alt="Rick Astley - Never Gonna Give You Up (Official Video)"
                    loading="lazy"
                >
                <div class="play-overlay">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            
            <div class="video-info">
                <h3 class="video-title">Rick Astley - Never Gonna Give You Up (Official Video)</h3>
                
                
                <div class="video-tags">
                    
                    <span class="tag">Music</span>
                    
                    <span class="tag">Classic</span>
                    
                    <span class="tag">80s</span>
                    
                    <span class="tag">Pop</span>
                    
                </div>
                
                
                <p class="video-date">15/1/2024</p>
            </div>
        </div>
        
        <div class="video-card fade-in" data-video-id="2" data-tags="music,k-pop,dance,viral">
            <div class="video-thumbnail">
                <img 
                    src="https://img.youtube.com/vi/9bZkp7q19f0/maxresdefault.jpg" 
                    alt="PSY - GANGNAM STYLE (강남스타일) M/V"
                    loading="lazy"
                >
                <div class="play-overlay">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            
            <div class="video-info">
                <h3 class="video-title">PSY - GANGNAM STYLE (강남스타일) M/V</h3>
                
                
                <div class="video-tags">
                    
                    <span class="tag">Music</span>
                    
                    <span class="tag">K-pop</span>
                    
                    <span class="tag">Dance</span>
                    
                    <span class="tag">Viral</span>
                    
                </div>
                
                
                <p class="video-date">14/1/2024</p>
            </div>
        </div>
        
        <div class="video-card fade-in" data-video-id="3" data-tags="music,latin,spanish,pop">
            <div class="video-thumbnail">
                <img 
                    src="https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg" 
                    alt="Luis Fonsi - Despacito ft. Daddy Yankee"
                    loading="lazy"
                >
                <div class="play-overlay">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            
            <div class="video-info">
                <h3 class="video-title">Luis Fonsi - Despacito ft. Daddy Yankee</h3>
                
                
                <div class="video-tags">
                    
                    <span class="tag">Music</span>
                    
                    <span class="tag">Latin</span>
                    
                    <span class="tag">Spanish</span>
                    
                    <span class="tag">Pop</span>
                    
                </div>
                
                
                <p class="video-date">13/1/2024</p>
            </div>
        </div>
        
    
</div>

<!-- Loading State -->
<div class="loading-state" id="loadingState" style="display: none;">
    <div class="loading"></div>
    <p>Loading videos...</p>
</div>

<!-- No Results State -->
<div class="no-results-state" id="noResultsState" style="display: none;">
    <div class="empty-state-content">
        <i class="fas fa-search empty-state-icon"></i>
        <h2>No videos found</h2>
        <p>Try adjusting your search terms or browse all videos.</p>
        <button class="btn clear-search-btn" id="clearSearchBtn">
            <i class="fas fa-times"></i>
            Clear Search
        </button>
    </div>
</div>

<style>
/* Empty State Styles */
.empty-state {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    text-align: center;
}

.empty-state-content {
    max-width: 400px;
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.empty-state h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.loading-state,
.no-results-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    text-align: center;
    color: var(--text-secondary);
}

.loading-state p,
.no-results-state p {
    margin-top: 1rem;
}

/* View Mode Specific Styles */
.video-grid[data-view="small-icons"] {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.video-grid[data-view="small-icons"] .video-thumbnail {
    height: 120px;
}

.video-grid[data-view="small-icons"] .video-title {
    font-size: 0.9rem;
    -webkit-line-clamp: 1;
}

.video-grid[data-view="list"] {
    grid-template-columns: 1fr;
    gap: 1rem;
}

.video-grid[data-view="list"] .video-card {
    display: flex;
    height: 120px;
}

.video-grid[data-view="list"] .video-thumbnail {
    width: 200px;
    height: 120px;
    flex-shrink: 0;
}

.video-grid[data-view="list"] .video-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.video-grid[data-view="details"] {
    grid-template-columns: 1fr;
    gap: 1rem;
}

.video-grid[data-view="details"] .video-card {
    display: flex;
    padding: 1rem;
}

.video-grid[data-view="details"] .video-thumbnail {
    width: 160px;
    height: 90px;
    flex-shrink: 0;
}

.video-grid[data-view="details"] .video-info {
    flex: 1;
    margin-left: 1rem;
}

.video-grid[data-view="tiles"] {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.video-grid[data-view="tiles"] .video-card {
    display: flex;
    flex-direction: row;
    height: 100px;
}

.video-grid[data-view="tiles"] .video-thumbnail {
    width: 120px;
    height: 100px;
    flex-shrink: 0;
}

.video-grid[data-view="tiles"] .video-info {
    flex: 1;
    padding: 0.75rem;
}

.video-grid[data-view="tiles"] .video-title {
    font-size: 0.9rem;
    -webkit-line-clamp: 2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .video-grid[data-view="list"] .video-card,
    .video-grid[data-view="details"] .video-card,
    .video-grid[data-view="tiles"] .video-card {
        flex-direction: column;
        height: auto;
    }
    
    .video-grid[data-view="list"] .video-thumbnail,
    .video-grid[data-view="details"] .video-thumbnail,
    .video-grid[data-view="tiles"] .video-thumbnail {
        width: 100%;
        height: 200px;
    }
    
    .video-grid[data-view="details"] .video-info,
    .video-grid[data-view="tiles"] .video-info {
        margin-left: 0;
        padding: 1rem;
    }
}
</style>

        </main>

        <!-- Add Video Modal -->
        <div class="modal" id="addVideoModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">Add New Video</h2>
                    <button class="close-btn" id="closeModalBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="addVideoForm">
                    <div class="form-group">
                        <label for="videoUrl" class="form-label">YouTube Video URL</label>
                        <input 
                            type="url" 
                            id="videoUrl" 
                            class="form-input" 
                            placeholder="https://www.youtube.com/watch?v=..."
                            required
                        >
                    </div>
                    
                    <div class="form-group" id="videoPreview" style="display: none;">
                        <div class="video-preview-card">
                            <img id="previewThumbnail" alt="Video thumbnail">
                            <div class="preview-info">
                                <h3 id="previewTitle"></h3>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" id="titleGroup" style="display: none;">
                        <label for="videoTitle" class="form-label">Video Title</label>
                        <input
                            type="text"
                            id="videoTitle"
                            class="form-input"
                            placeholder="Enter video title"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="videoTags" class="form-label">Tags (separate with commas)</label>
                        <input
                            type="text"
                            id="videoTags"
                            class="form-input"
                            placeholder="e.g., Tutorial, JavaScript, Web Development"
                        >
                        <div class="tag-suggestions" id="tagSuggestions"></div>

                        <div class="popular-tags" id="popularTags">
                            <label class="form-label">Popular Tags (click to add):</label>
                            <div class="popular-tags-list" id="popularTagsList"></div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn" id="saveVideoBtn">
                        <i class="fas fa-save"></i>
                        Save Video
                    </button>
                </form>
            </div>
        </div>

        <!-- Video Detail Modal -->
        <div class="modal" id="videoDetailModal">
            <div class="modal-content video-detail-content">
                <div class="modal-header">
                    <h2 class="modal-title" id="detailTitle"></h2>
                    <button class="close-btn" id="closeDetailBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="video-detail-body">
                    <div class="video-player-container">
                        <iframe 
                            id="videoPlayer" 
                            width="100%" 
                            height="315" 
                            frameborder="0" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    
                    <div class="video-detail-info">
                        <div class="video-tags" id="detailTags"></div>
                        <p class="video-date" id="detailDate"></p>
                        
                        <div class="video-actions">
                            <a href="#" class="btn" id="openYouTubeBtn" target="_blank">
                                <i class="fab fa-youtube"></i>
                                Open in YouTube
                            </a>
                            <button class="btn" id="copyLinkBtn">
                                <i class="fas fa-copy"></i>
                                Copy Link
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/swiper-bundle.min.js"></script>
    <script src="/motion.min.js"></script>
    <script src="/barba.min.js"></script>
    <script src="/lottie.min.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>
