// YouTube Link Saver App
class YouTubeLinkSaver {
    constructor() {
        this.videos = [];
        this.tags = [];
        this.currentView = 'large-icons';
        this.searchTerm = '';
        
        this.init();
    }

    init() {
        this.loadData();
        this.bindEvents();
        this.initializeAnimations();
        this.initializeBarba();
    }

    // Load data from localStorage or initialize empty arrays
    loadData() {
        const savedVideos = localStorage.getItem('youtube-saver-videos');
        const savedTags = localStorage.getItem('youtube-saver-tags');
        
        this.videos = savedVideos ? JSON.parse(savedVideos) : [];
        this.tags = savedTags ? JSON.parse(savedTags) : [];
        
        this.updateResultsCount();
    }

    // Save data to localStorage
    saveData() {
        localStorage.setItem('youtube-saver-videos', JSON.stringify(this.videos));
        localStorage.setItem('youtube-saver-tags', JSON.stringify(this.tags));
    }

    // Bind event listeners
    bindEvents() {
        // Modal controls
        const addVideoBtn = document.getElementById('addVideoBtn');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const closeDetailBtn = document.getElementById('closeDetailBtn');
        const addVideoModal = document.getElementById('addVideoModal');
        const videoDetailModal = document.getElementById('videoDetailModal');

        if (addVideoBtn) {
            addVideoBtn.addEventListener('click', () => this.openAddVideoModal());
        }

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => this.closeModal('addVideoModal'));
        }

        if (closeDetailBtn) {
            closeDetailBtn.addEventListener('click', () => this.closeModal('videoDetailModal'));
        }

        // Close modal when clicking outside
        [addVideoModal, videoDetailModal].forEach(modal => {
            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.closeModal(modal.id);
                    }
                });
            }
        });

        // Form submission
        const addVideoForm = document.getElementById('addVideoForm');
        if (addVideoForm) {
            addVideoForm.addEventListener('submit', (e) => this.handleAddVideo(e));
        }

        // Video URL input for preview
        const videoUrlInput = document.getElementById('videoUrl');
        if (videoUrlInput) {
            videoUrlInput.addEventListener('input', (e) => this.handleUrlInput(e));
        }

        // Tags input for suggestions
        const videoTagsInput = document.getElementById('videoTags');
        if (videoTagsInput) {
            videoTagsInput.addEventListener('input', (e) => this.handleTagsInput(e));
            videoTagsInput.addEventListener('keydown', (e) => this.handleTagsKeydown(e));
        }

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e));
        }

        // View mode controls
        const viewModeButtons = document.querySelectorAll('.view-mode-btn');
        viewModeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleViewModeChange(e));
        });

        // Video card clicks
        this.bindVideoCardEvents();

        // Clear search button
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => this.clearSearch());
        }

        // Add first video button
        const addFirstVideoBtn = document.querySelector('.add-first-video-btn');
        if (addFirstVideoBtn) {
            addFirstVideoBtn.addEventListener('click', () => this.openAddVideoModal());
        }

        // Copy link functionality
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', () => this.copyVideoLink());
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    // Bind video card click events
    bindVideoCardEvents() {
        const videoCards = document.querySelectorAll('.video-card');
        videoCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const videoId = card.dataset.videoId;
                this.openVideoDetail(videoId);
            });
        });
    }

    // Extract YouTube video ID from URL
    extractYouTubeId(url) {
        const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(regex);
        return match ? match[1] : null;
    }

    // Fetch video metadata from YouTube
    async fetchVideoMetadata(videoId) {
        try {
            const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;

            // Try to fetch video title using oEmbed API
            let title = 'YouTube Video';
            try {
                const oEmbedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;
                const response = await fetch(oEmbedUrl);
                if (response.ok) {
                    const data = await response.json();
                    title = data.title || title;
                }
            } catch (oEmbedError) {
                console.log('oEmbed fetch failed, using default title');
            }

            return {
                title: title,
                thumbnail: thumbnailUrl,
                videoId: videoId
            };
        } catch (error) {
            console.error('Error fetching video metadata:', error);
            return null;
        }
    }

    // Handle URL input for video preview
    async handleUrlInput(e) {
        const url = e.target.value.trim();
        const videoId = this.extractYouTubeId(url);
        const previewContainer = document.getElementById('videoPreview');
        
        if (videoId) {
            const metadata = await this.fetchVideoMetadata(videoId);
            if (metadata) {
                this.showVideoPreview(metadata);
            }
        } else {
            previewContainer.style.display = 'none';
        }
    }

    // Show video preview
    showVideoPreview(metadata) {
        const previewContainer = document.getElementById('videoPreview');
        const previewThumbnail = document.getElementById('previewThumbnail');
        const previewTitle = document.getElementById('previewTitle');
        const titleGroup = document.getElementById('titleGroup');
        const titleInput = document.getElementById('videoTitle');

        previewThumbnail.src = metadata.thumbnail;
        previewTitle.textContent = metadata.title;
        previewContainer.style.display = 'block';

        // Show title input and populate it
        if (titleGroup && titleInput) {
            titleInput.value = metadata.title;
            titleGroup.style.display = 'block';
        }
    }

    // Handle tags input for suggestions
    handleTagsInput(e) {
        const input = e.target.value;
        const lastTag = input.split(',').pop().trim().toLowerCase();

        if (lastTag.length > 0) {
            this.showTagSuggestions(lastTag);
        } else {
            this.hideTagSuggestions();
        }

        // Update popular tags state
        this.updatePopularTagsState();
    }

    // Handle tags keydown for navigation
    handleTagsKeydown(e) {
        const suggestions = document.querySelectorAll('.tag-suggestion');
        const activeSuggestion = document.querySelector('.tag-suggestion.active');
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (activeSuggestion) {
                activeSuggestion.classList.remove('active');
                const next = activeSuggestion.nextElementSibling;
                if (next) {
                    next.classList.add('active');
                } else {
                    suggestions[0]?.classList.add('active');
                }
            } else {
                suggestions[0]?.classList.add('active');
            }
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (activeSuggestion) {
                activeSuggestion.classList.remove('active');
                const prev = activeSuggestion.previousElementSibling;
                if (prev) {
                    prev.classList.add('active');
                } else {
                    suggestions[suggestions.length - 1]?.classList.add('active');
                }
            } else {
                suggestions[suggestions.length - 1]?.classList.add('active');
            }
        } else if (e.key === 'Enter' && activeSuggestion) {
            e.preventDefault();
            this.selectTagSuggestion(activeSuggestion.textContent);
        } else if (e.key === 'Escape') {
            this.hideTagSuggestions();
        }
    }

    // Show tag suggestions
    showTagSuggestions(searchTerm) {
        const suggestionsContainer = document.getElementById('tagSuggestions');
        const tagsInput = document.getElementById('videoTags');
        const currentTags = tagsInput.value.split(',').map(tag => tag.trim().toLowerCase());

        // Filter tags that match search term and aren't already added
        const matchingTags = this.tags.filter(tag => {
            const tagLower = tag.toLowerCase();
            return tagLower.includes(searchTerm) &&
                   tagLower !== searchTerm &&
                   !currentTags.includes(tagLower);
        });

        if (matchingTags.length > 0) {
            suggestionsContainer.innerHTML = matchingTags
                .slice(0, 5) // Limit to 5 suggestions
                .map(tag => `<div class="tag-suggestion">${tag}</div>`)
                .join('');

            // Bind click events to suggestions
            suggestionsContainer.querySelectorAll('.tag-suggestion').forEach(suggestion => {
                suggestion.addEventListener('click', () => {
                    this.selectTagSuggestion(suggestion.textContent);
                });

                // Add hover effects
                suggestion.addEventListener('mouseenter', () => {
                    // Remove active class from all suggestions
                    suggestionsContainer.querySelectorAll('.tag-suggestion').forEach(s => {
                        s.classList.remove('active');
                    });
                    suggestion.classList.add('active');
                });
            });

            suggestionsContainer.classList.add('active');
        } else {
            this.hideTagSuggestions();
        }
    }

    // Select a tag suggestion
    selectTagSuggestion(tag) {
        const tagsInput = document.getElementById('videoTags');
        const currentTags = tagsInput.value.split(',');
        currentTags[currentTags.length - 1] = ` ${tag}`;
        tagsInput.value = currentTags.join(',');
        this.hideTagSuggestions();
        tagsInput.focus();
    }

    // Hide tag suggestions
    hideTagSuggestions() {
        const suggestionsContainer = document.getElementById('tagSuggestions');
        suggestionsContainer.classList.remove('active');
    }

    // Show popular tags
    showPopularTags() {
        const popularTagsList = document.getElementById('popularTagsList');
        if (!popularTagsList) return;

        // Get most popular tags (first 10)
        const popularTags = this.tags.slice(0, 10);

        popularTagsList.innerHTML = popularTags
            .map(tag => `<span class="popular-tag" data-tag="${tag}">${tag}</span>`)
            .join('');

        // Bind click events to popular tags
        popularTagsList.querySelectorAll('.popular-tag').forEach(tagElement => {
            tagElement.addEventListener('click', () => {
                const tag = tagElement.dataset.tag;
                this.addTagToInput(tag);
                this.updatePopularTagsState();
            });
        });

        this.updatePopularTagsState();
    }

    // Add tag to input
    addTagToInput(tag) {
        const tagsInput = document.getElementById('videoTags');
        const currentValue = tagsInput.value.trim();

        // Check if tag is already added
        const currentTags = currentValue.split(',').map(t => t.trim().toLowerCase());
        if (currentTags.includes(tag.toLowerCase())) {
            return; // Tag already exists
        }

        // Add tag to input
        if (currentValue) {
            tagsInput.value = currentValue + ', ' + tag;
        } else {
            tagsInput.value = tag;
        }

        // Trigger input event to update suggestions
        tagsInput.dispatchEvent(new Event('input'));
    }

    // Update popular tags state (show which ones are already added)
    updatePopularTagsState() {
        const tagsInput = document.getElementById('videoTags');
        const popularTagsList = document.getElementById('popularTagsList');

        if (!tagsInput || !popularTagsList) return;

        const currentTags = tagsInput.value.split(',').map(t => t.trim().toLowerCase());

        popularTagsList.querySelectorAll('.popular-tag').forEach(tagElement => {
            const tag = tagElement.dataset.tag.toLowerCase();
            if (currentTags.includes(tag)) {
                tagElement.classList.add('added');
            } else {
                tagElement.classList.remove('added');
            }
        });
    }

    // Capitalize words in a string
    capitalizeWords(str) {
        return str.replace(/\w\S*/g, (txt) => 
            txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
    }

    // Handle add video form submission
    async handleAddVideo(e) {
        e.preventDefault();

        const urlInput = document.getElementById('videoUrl');
        const titleInput = document.getElementById('videoTitle');
        const tagsInput = document.getElementById('videoTags');
        const saveBtn = document.getElementById('saveVideoBtn');

        const url = urlInput.value.trim();
        const customTitle = titleInput ? titleInput.value.trim() : '';
        const videoId = this.extractYouTubeId(url);

        if (!videoId) {
            alert('Please enter a valid YouTube URL');
            return;
        }

        if (!customTitle) {
            alert('Please enter a video title');
            return;
        }

        // Disable save button and show loading
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<div class="loading"></div> Saving...';

        try {
            const metadata = await this.fetchVideoMetadata(videoId);
            if (!metadata) {
                throw new Error('Could not fetch video metadata');
            }

            // Process tags
            const rawTags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);
            const processedTags = rawTags.map(tag => this.capitalizeWords(tag));

            // Add new tags to the tags array
            processedTags.forEach(tag => {
                if (!this.tags.includes(tag)) {
                    this.tags.push(tag);
                }
            });

            // Create video object
            const video = {
                id: Date.now().toString(),
                url: url,
                videoId: videoId,
                title: customTitle || metadata.title, // Use custom title if provided
                thumbnail: metadata.thumbnail,
                tags: processedTags,
                dateAdded: new Date().toISOString()
            };

            // Add video to collection
            this.videos.unshift(video); // Add to beginning
            this.saveData();

            // Close modal and refresh page
            this.closeModal('addVideoModal');
            this.refreshVideoGrid();

            // Show success message
            this.showNotification('Video added successfully!', 'success');

        } catch (error) {
            console.error('Error adding video:', error);
            alert('Error adding video. Please try again.');
        } finally {
            // Re-enable save button
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Video';
        }
    }

    // Open add video modal
    openAddVideoModal() {
        const modal = document.getElementById('addVideoModal');
        modal.classList.add('active');

        // Reset form
        document.getElementById('addVideoForm').reset();
        document.getElementById('videoPreview').style.display = 'none';

        // Hide title group
        const titleGroup = document.getElementById('titleGroup');
        if (titleGroup) {
            titleGroup.style.display = 'none';
        }

        this.hideTagSuggestions();
        this.showPopularTags();

        // Focus on URL input
        setTimeout(() => {
            document.getElementById('videoUrl').focus();
        }, 100);
    }

    // Close modal
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.classList.remove('active');
    }

    // Handle search
    handleSearch(e) {
        this.searchTerm = e.target.value.toLowerCase().trim();
        this.filterVideos();
    }

    // Clear search
    clearSearch() {
        const searchInput = document.getElementById('searchInput');
        searchInput.value = '';
        this.searchTerm = '';
        this.filterVideos();
        searchInput.focus();
    }

    // Filter videos based on search term
    filterVideos() {
        const videoCards = document.querySelectorAll('.video-card');
        const noResultsState = document.getElementById('noResultsState');
        let visibleCount = 0;

        videoCards.forEach((card, index) => {
            const title = card.querySelector('.video-title').textContent.toLowerCase();
            const tags = card.dataset.tags.toLowerCase();

            const matches = !this.searchTerm ||
                           title.includes(this.searchTerm) ||
                           tags.includes(this.searchTerm);

            if (matches) {
                // Animate in with stagger
                card.style.display = 'block';
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);

                visibleCount++;

                // Highlight search terms
                this.highlightSearchTerm(card);
            } else {
                card.style.display = 'none';
            }
        });

        // Show/hide no results state
        if (noResultsState) {
            if (visibleCount === 0 && this.searchTerm) {
                noResultsState.style.display = 'flex';
            } else {
                noResultsState.style.display = 'none';
            }
        }

        this.updateResultsCount(visibleCount);
    }

    // Highlight search terms in video cards
    highlightSearchTerm(card) {
        const titleElement = card.querySelector('.video-title');
        const originalTitle = titleElement.dataset.originalTitle || titleElement.textContent;

        // Store original title if not already stored
        if (!titleElement.dataset.originalTitle) {
            titleElement.dataset.originalTitle = originalTitle;
        }

        if (this.searchTerm) {
            const regex = new RegExp(`(${this.escapeRegex(this.searchTerm)})`, 'gi');
            const highlightedTitle = originalTitle.replace(regex, '<mark>$1</mark>');
            titleElement.innerHTML = highlightedTitle;
        } else {
            titleElement.textContent = originalTitle;
        }
    }

    // Escape special regex characters
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Ctrl/Cmd + N to add new video
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.openAddVideoModal();
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                this.closeModal(activeModal.id);
            }
        }

        // Ctrl/Cmd + / to clear search
        if ((e.ctrlKey || e.metaKey) && e.key === '/') {
            e.preventDefault();
            this.clearSearch();
        }
    }

    // Update results count
    updateResultsCount(count = null) {
        const resultsCount = document.getElementById('resultsCount');
        if (resultsCount) {
            const displayCount = count !== null ? count : this.videos.length;
            const suffix = this.searchTerm ? ' found' : '';
            resultsCount.textContent = `${displayCount} video${displayCount !== 1 ? 's' : ''}${suffix}`;
        }
    }

    // Handle view mode change
    handleViewModeChange(e) {
        const button = e.currentTarget;
        const viewMode = button.dataset.view;
        
        // Update active button
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');
        
        // Update grid view
        const videoGrid = document.getElementById('videoGrid');
        if (videoGrid) {
            videoGrid.dataset.view = viewMode;
            this.currentView = viewMode;
        }
        
        // Animate the change
        this.animateViewChange();
    }

    // Animate view change
    animateViewChange() {
        const videoCards = document.querySelectorAll('.video-card');
        const videoGrid = document.getElementById('videoGrid');

        // Add a subtle scale animation to the grid
        if (videoGrid) {
            videoGrid.style.transform = 'scale(0.98)';
            videoGrid.style.opacity = '0.8';

            setTimeout(() => {
                videoGrid.style.transform = 'scale(1)';
                videoGrid.style.opacity = '1';
            }, 150);
        }

        // Animate individual cards
        videoCards.forEach((card, index) => {
            card.style.opacity = '0.3';
            card.style.transform = 'translateY(10px) scale(0.95)';

            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0) scale(1)';
            }, index * 30 + 100);
        });
    }

    // Open video detail modal
    openVideoDetail(videoId) {
        const video = this.videos.find(v => v.id === videoId);
        if (!video) return;
        
        const modal = document.getElementById('videoDetailModal');
        const title = document.getElementById('detailTitle');
        const player = document.getElementById('videoPlayer');
        const tags = document.getElementById('detailTags');
        const date = document.getElementById('detailDate');
        const youtubeBtn = document.getElementById('openYouTubeBtn');
        
        // Set content
        title.textContent = video.title;
        player.src = `https://www.youtube.com/embed/${video.videoId}`;
        
        // Set tags
        tags.innerHTML = video.tags.map(tag => 
            `<span class="tag">${tag}</span>`
        ).join('');
        
        // Set date
        date.textContent = `Added ${new Date(video.dateAdded).toLocaleDateString()}`;
        
        // Set YouTube link
        youtubeBtn.href = video.url;
        
        // Store current video for copy link functionality
        this.currentDetailVideo = video;
        
        modal.classList.add('active');
    }

    // Copy video link
    copyVideoLink() {
        if (this.currentDetailVideo) {
            navigator.clipboard.writeText(this.currentDetailVideo.url).then(() => {
                this.showNotification('Link copied to clipboard!', 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = this.currentDetailVideo.url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showNotification('Link copied to clipboard!', 'success');
            });
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add styles
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: type === 'success' ? 'var(--success-color)' : 'var(--primary-color)',
            color: 'white',
            padding: '1rem 1.5rem',
            borderRadius: 'var(--border-radius)',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Refresh video grid (for when new videos are added)
    refreshVideoGrid() {
        // In a real app, this would re-render the grid
        // For now, we'll just reload the page
        window.location.reload();
    }

    // Initialize animations
    initializeAnimations() {
        // Add entrance animations to video cards
        const videoCards = document.querySelectorAll('.video-card');
        videoCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Initialize Barba.js for page transitions
    initializeBarba() {
        if (typeof barba !== 'undefined') {
            barba.init({
                transitions: [{
                    name: 'opacity-transition',
                    leave(data) {
                        return new Promise(resolve => {
                            data.current.container.style.opacity = '0';
                            setTimeout(resolve, 300);
                        });
                    },
                    enter(data) {
                        return new Promise(resolve => {
                            data.next.container.style.opacity = '0';
                            setTimeout(() => {
                                data.next.container.style.opacity = '1';
                                resolve();
                            }, 300);
                        });
                    }
                }]
            });
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new YouTubeLinkSaver();
});
